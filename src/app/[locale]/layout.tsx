import { AuthProvider } from "@/contexts/AuthContext";
import { SettingsProvider } from "@/contexts/SettingsContext";
import { LocaleProvider } from "@/contexts/LocaleContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { OrdersProvider } from "@/contexts/OrdersContext";
import { ToastProvider } from "@/components/ui/Toast";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { locales, type Locale } from '@/i18n/config';
import LocaleHtmlAttributes from '@/components/LocaleHtmlAttributes';

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  // Providing all messages to the client side is the easiest way to get started
  const messages = await getMessages({ locale: locale as Locale });

  return (
    <NextIntlClientProvider messages={messages} locale={locale as Locale}>
      <AuthProvider>
        <SettingsProvider>
          <LocaleProvider initialLocale={locale as Locale}>
            <NotificationProvider>
              <OrdersProvider>
                <ToastProvider>
                  <LocaleHtmlAttributes />
                  {children}
                </ToastProvider>
              </OrdersProvider>
            </NotificationProvider>
          </LocaleProvider>
        </SettingsProvider>
      </AuthProvider>
    </NextIntlClientProvider>
  );
}
