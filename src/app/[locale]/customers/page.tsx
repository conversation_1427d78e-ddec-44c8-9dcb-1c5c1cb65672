'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useCustomersTranslations, useCommonTranslations, useConfirmationTranslations, useToastTranslations } from '@/hooks/useTranslations';
import { useConfirmationDialog } from '@/components/ui/ConfirmationDialog';
import { useToastHelpers } from '@/components/ui/Toast';
import {
  Users,
  Star,
  UserCheck,
  UserX,
  Search,
  MapPin,
  Phone,
  Mail,
  Calendar,
  ShoppingBag,
  DollarSign,
  Plus,
  Edit,
  Trash2,

  X,
  Save
} from 'lucide-react';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';


import { SettingsModal } from '@/components/SettingsModal';

// Customer types
type CustomerStatus = 'Active' | 'Inactive';

type Customer = {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  avatar?: string;
  status: CustomerStatus;
  isVIP: boolean;
  joinDate: string;
  totalOrders: number;
  totalSpent: string;
  lastOrderDate?: string;
  notes?: string;
  preferredPaymentMethod?: 'Cash' | 'Card' | 'Digital' | 'Other';
  location?: {
    lat: number;
    lng: number;
  };
};

// Status styling function
const getStatusClass = (status: CustomerStatus) => {
  switch (status) {
    case 'Active':
      return 'bg-green-100 text-green-800';
    case 'Inactive':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Customer Row Component
const CustomerRow: React.FC<{
  customer: Customer;
  loading?: boolean;
  index: number;
  onClick?: (customer: Customer) => void;
  onToggleVip?: (customerId: string, isVIP: boolean) => void;
}> = ({ customer, loading = false, onClick, onToggleVip }) => {
  const t = useCustomersTranslations();
  if (loading) {
    return (
      <tr className="border-b border-gray-100 animate-pulse">
        <td className="py-4 px-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
            <div>
              <div className="h-4 bg-gray-200 rounded w-24 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-32"></div>
            </div>
          </div>
        </td>
        <td className="py-4 px-6"><div className="h-4 bg-gray-200 rounded w-16"></div></td>
        <td className="py-4 px-6"><div className="h-4 bg-gray-200 rounded w-20"></div></td>
        <td className="py-4 px-6"><div className="h-4 bg-gray-200 rounded w-16"></div></td>
        <td className="py-4 px-6"><div className="h-4 bg-gray-200 rounded w-20"></div></td>
        <td className="py-4 px-6"><div className="h-4 bg-gray-200 rounded w-24"></div></td>
      </tr>
    );
  }

  const handleRowClick = (e: React.MouseEvent) => {
    // Don't trigger row click if star was clicked
    if ((e.target as HTMLElement).closest('.vip-star')) {
      return;
    }
    onClick?.(customer);
  };

  const handleVipToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleVip?.(customer.id, !customer.isVIP);
  };

  return (
    <tr
      className="border-b border-gray-100 hover:bg-gray-50 transition-colors group cursor-pointer"
      onClick={handleRowClick}
    >
      <td className="py-4 px-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
            {customer.avatar ? (
              <Image src={customer.avatar} alt={customer.name} width={40} height={40} className="w-full h-full rounded-full object-cover" />
            ) : (
              customer.name.charAt(0)
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-gray-900 group-hover:text-indigo-600">{customer.name}</p>
              <button
                onClick={handleVipToggle}
                className={`vip-star transition-colors ${
                  customer.isVIP
                    ? 'text-yellow-500 hover:text-yellow-600'
                    : 'text-gray-300 hover:text-yellow-400'
                }`}
                title={customer.isVIP ? 'Remove VIP status' : 'Make VIP customer'}
              >
                <Star className={`w-4 h-4 ${customer.isVIP ? 'fill-current' : ''}`} />
              </button>
            </div>
            <p className="text-xs text-gray-500 flex items-center gap-1">
              <Mail className="w-3 h-3" />
              {customer.email}
            </p>
          </div>
        </div>
      </td>
      <td className="py-4 px-6">
        <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getStatusClass(customer.status)} group-hover:shadow-sm transition-shadow`}>
          {customer.status === 'Active' ? t('status.active') : t('status.inactive')}
        </span>
      </td>
      <td className="py-4 px-6 text-sm text-gray-500 group-hover:text-gray-600">
        <div className="flex items-center gap-1">
          <Phone className="w-3 h-3" />
          {customer.phone}
        </div>
      </td>
      <td className="py-4 px-6 text-sm text-gray-500 group-hover:text-gray-600">
        <div className="flex items-center gap-1">
          <ShoppingBag className="w-3 h-3" />
          {customer.totalOrders}
        </div>
      </td>
      <td className="py-4 px-6 text-sm font-semibold text-gray-900 group-hover:text-indigo-600">
        <div className="flex items-center gap-1">
          <DollarSign className="w-3 h-3" />
          {customer.totalSpent}
        </div>
      </td>
      <td className="py-4 px-6 text-sm text-gray-500 group-hover:text-gray-600">
        <div className="flex items-center gap-1">
          <Calendar className="w-3 h-3" />
          {customer.lastOrderDate || 'Never'}
        </div>
      </td>
    </tr>
  );
};

// Customer Details Modal Component
const CustomerDetailsModal: React.FC<{
  customer: Customer | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (customer: Customer) => void;
  onDelete: (customerId: string) => void;
}> = ({ customer, isOpen, onClose, onSave, onDelete }) => {
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState<Partial<Customer>>({});
  const t = useCustomersTranslations();
  const confirmationT = useConfirmationTranslations();
  const toastT = useToastTranslations();
  const common = useCommonTranslations();
  const { showConfirmation, ConfirmationDialogComponent } = useConfirmationDialog();
  const { success, error } = useToastHelpers();

  useEffect(() => {
    if (customer) {
      setFormData(customer);
      setEditMode(false);
    }
  }, [customer]);

  const handleSave = async () => {
    if (!customer || !formData.name || !formData.email || !formData.phone || !formData.address) return;

    try {
      const response = await fetch('/api/customers', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...formData, id: customer.id })
      });

      if (response.ok) {
        const data = await response.json();
        onSave(data.data);
        setEditMode(false);
      }
    } catch (error) {
      console.error('Error updating customer:', error);
    }
  };

  // Deactivate customer (preserves data integrity)
  const handleDeactivate = async () => {
    if (!customer) return;

    showConfirmation({
      title: confirmationT('deactivateCustomer.title'),
      message: confirmationT('deactivateCustomer.message'),
      type: 'warning',
      confirmText: common('deactivate'),
      showCancel: false, // Use single button mode
      onConfirm: async () => {
        try {
          const response = await fetch('/api/customers', {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              id: customer.id,
              status: 'Inactive'
            })
          });

          if (response.ok) {
            onDelete(customer.id); // This will trigger a refresh
            onClose();
            success(toastT('customerDeactivated'));
          }
        } catch (err) {
          console.error('Error deactivating customer:', err);
          error(toastT('operationFailed'));
        }
      }
    });
  };

  // Legacy method for backward compatibility
  const handleDelete = async () => {
    return handleDeactivate();
  };

  if (!isOpen || !customer) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Customer Details</h2>
            <div className="flex items-center gap-2">
              {editMode ? (
                <>
                  <button
                    onClick={handleSave}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    Save
                  </button>
                  <button
                    onClick={() => setEditMode(false)}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Cancel
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setEditMode(true)}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
                  >
                    <Edit className="w-4 h-4" />
                    Edit
                  </button>
                  <button
                    onClick={handleDelete}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </button>
                </>
              )}
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {editMode ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                <input
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                <input
                  type="tel"
                  value={formData.phone || ''}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={formData.status || 'Active'}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as CustomerStatus })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">VIP Status</label>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, isVIP: !formData.isVIP })}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                      formData.isVIP
                        ? 'bg-yellow-50 border-yellow-300 text-yellow-700'
                        : 'bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <Star className={`w-4 h-4 ${formData.isVIP ? 'fill-current text-yellow-500' : 'text-gray-400'}`} />
                    {formData.isVIP ? 'VIP Customer' : 'Regular Customer'}
                  </button>
                </div>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <textarea
                  value={formData.address || ''}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea
                  value={formData.notes || ''}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {customer.avatar ? (
                    <Image src={customer.avatar} alt={customer.name} width={64} height={64} className="w-full h-full rounded-full object-cover" />
                  ) : (
                    customer.name.charAt(0)
                  )}
                </div>
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-xl font-semibold text-gray-900">{customer.name}</h3>
                    {customer.isVIP && (
                      <Star className="w-5 h-5 text-yellow-500 fill-current" />
                    )}
                  </div>
                  <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getStatusClass(customer.status)}`}>
                    {customer.status}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.email}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.phone}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.address}</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <ShoppingBag className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.totalOrders} {t('ordersText')}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <DollarSign className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.totalSpent}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Calendar className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{t('joined')} {customer.joinDate}</span>
                  </div>
                </div>
              </div>

              {customer.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">{t('notes')}</h4>
                  <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">{customer.notes}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialogComponent />
    </div>
  );
};

// Add Customer Modal Component
const AddCustomerModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (customer: Customer) => void;
}> = ({ isOpen, onClose, onSave }) => {
  const t = useCustomersTranslations();
  const common = useCommonTranslations();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    status: 'Active' as CustomerStatus,
    isVIP: false,
    preferredPaymentMethod: 'Cash' as 'Cash' | 'Card' | 'Digital' | 'Other',
    notes: ''
  });

  const handleSave = async () => {
    if (!formData.name || !formData.email || !formData.phone || !formData.address) return;

    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const data = await response.json();
        onSave(data.data);
        setFormData({
          name: '',
          email: '',
          phone: '',
          address: '',
          status: 'Active',
          isVIP: false,
          preferredPaymentMethod: 'Cash',
          notes: ''
        });
        onClose();
      }
    } catch (error) {
      console.error('Error creating customer:', error);
    }
  };

  if (!isOpen) return null;

  // Handle click on the backdrop to close the modal
  const handleBackdropClick = (e: React.MouseEvent) => {
    // Only close if clicking the backdrop, not the modal content
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">{t('addNewCustomer')}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{common('name')} *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder={t('enterCustomerName')}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{common('email')} *</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder={t('enterEmailAddress')}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{common('phone')} *</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder={t('enterPhoneNumber')}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{common('status')}</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as CustomerStatus })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="Active">{t('status.active')}</option>
                <option value="Inactive">{t('status.inactive')}</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('vipStatus')}</label>
              <div className="flex items-center gap-3">
                <button
                  type="button"
                  onClick={() => setFormData({ ...formData, isVIP: !formData.isVIP })}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                    formData.isVIP
                      ? 'bg-yellow-50 border-yellow-300 text-yellow-700'
                      : 'bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Star className={`w-4 h-4 ${formData.isVIP ? 'fill-current text-yellow-500' : 'text-gray-400'}`} />
                  {formData.isVIP ? t('vip') : t('regularCustomer')}
                </button>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('preferredPayment')}</label>
              <select
                value={formData.preferredPaymentMethod}
                onChange={(e) => setFormData({ ...formData, preferredPaymentMethod: e.target.value as 'Cash' | 'Card' | 'Digital' | 'Other' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="Cash">Cash</option>
                <option value="Card">Card</option>
                <option value="Digital">Digital</option>
                <option value="Other">Other</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">{common('address')} *</label>
              <textarea
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder={t('enterCustomerAddress')}
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('notes')}</label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder={t('additionalNotesAboutCustomer')}
              />
            </div>
          </div>

          <div className="flex justify-center mt-6">
            <button
              onClick={handleSave}
              disabled={!formData.name || !formData.email || !formData.phone || !formData.address}
              className="px-8 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              {t('addCustomer')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Data fetching function
const fetchCustomers = async (status?: string, search?: string): Promise<Customer[]> => {
  try {
    const params = new URLSearchParams();
    if (status && status !== 'All') params.append('status', status);
    if (search) params.append('search', search);

    const response = await fetch(`/api/customers?${params.toString()}`);
    const data = await response.json();
    return data.success ? data.data : [];
  } catch (error) {
    console.error('Error fetching customers:', error);
    return [];
  }
};

// Main Customers Page Component
const CustomersPage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const t = useCustomersTranslations();
  const common = useCommonTranslations();
  const [activeItem, setActiveItem] = useState('customers');
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const [showSettings, setShowSettings] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showAddCustomerModal, setShowAddCustomerModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Handle navigation
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  // Load customers
  const loadCustomers = useCallback(async () => {
    try {
      setLoading(true);
      const data = await fetchCustomers(statusFilter, searchTerm);
      setCustomers(data);
    } catch (error) {
      console.error('Error loading customers:', error);
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  }, [statusFilter, searchTerm]);

  // Handle customer click
  const handleCustomerClick = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerModal(true);
  };

  // Handle customer save
  const handleCustomerSave = (updatedCustomer: Customer) => {
    setCustomers(prev => prev.map(c => c.id === updatedCustomer.id ? updatedCustomer : c));
    setSelectedCustomer(updatedCustomer);
  };

  // Handle customer delete
  const handleCustomerDelete = (customerId: string) => {
    setCustomers(prev => prev.filter(c => c.id !== customerId));
    setSelectedCustomer(null);
  };

  // Handle add customer
  const handleAddCustomer = (newCustomer: Customer) => {
    setCustomers(prev => [newCustomer, ...prev]);
  };

  // Handle VIP toggle
  const handleToggleVip = async (customerId: string, isVIP: boolean) => {
    try {
      const response = await fetch('/api/customers', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: customerId, isVip: isVIP })
      });

      if (response.ok) {
        const data = await response.json();
        setCustomers(prev => prev.map(c => c.id === customerId ? data.data : c));
        if (selectedCustomer?.id === customerId) {
          setSelectedCustomer(data.data);
        }
      }
    } catch (error) {
      console.error('Error toggling VIP status:', error);
    }
  };

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  // Load initial customers
  useEffect(() => {
    if (user) {
      loadCustomers();
    }
  }, [user, statusFilter, searchTerm, loadCustomers]);

  if (authLoading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  if (!user) {
    return null;
  }

  // Calculate statistics
  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(c => c.status === 'Active').length;
  const vipCustomers = customers.filter(c => c.isVIP).length;
  const inactiveCustomers = customers.filter(c => c.status === 'Inactive').length;
  const totalRevenue = customers.reduce((sum, customer) => {
    const amount = parseFloat(customer.totalSpent.replace(/[^\d.]/g, '')) || 0;
    return sum + amount;
  }, 0);

  return (
    <PageTransition transitionKey="customers" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-8 pt-25">
            {/* Creative Add New Customer Section */}
            <div className="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-8 mb-8 overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-0 left-0 w-40 h-40 bg-white rounded-full -translate-x-20 -translate-y-20"></div>
                <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-16 translate-y-16"></div>
              </div>

              <div className="relative z-10 flex flex-col md:flex-row items-center justify-between text-white">
                <div className="mb-6 md:mb-0">
                  <h2 className="text-3xl font-bold mb-2">{t('readyToAdd')}</h2>
                  <p className="text-lg opacity-90 mb-4">{t('expandCustomerBase')}</p>
                  <div className="flex items-center gap-4 text-sm opacity-80">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      <span>{t('customerManagement')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4" />
                      <span>{t('vipProgram')}</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => setShowAddCustomerModal(true)}
                  className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-semibold hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center gap-3"
                >
                  <Plus className="w-5 h-5" />
                  {t('newCustomer')}
                </button>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('totalCustomers')}</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {loading ? '...' : totalCustomers}
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-indigo-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('activeCustomers')}</p>
                    <p className="text-3xl font-bold text-green-600">
                      {loading ? '...' : activeCustomers}
                    </p>
                  </div>
                  <UserCheck className="w-8 h-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('vipCustomers')}</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {loading ? '...' : vipCustomers}
                    </p>
                  </div>
                  <Star className="w-8 h-8 text-purple-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('inactiveCustomers')}</p>
                    <p className="text-3xl font-bold text-red-600">
                      {loading ? '...' : inactiveCustomers}
                    </p>
                  </div>
                  <UserX className="w-8 h-8 text-red-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('totalRevenue')}</p>
                    <p className="text-3xl font-bold text-orange-600">
                      {loading ? '...' : `${totalRevenue.toFixed(0)} EGP`}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-orange-600" />
                </div>
              </div>
            </div>

            {/* Customers Table */}
            <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
              {/* Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Users className="w-6 h-6 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">{t('title')}</h3>
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                      {loading ? '...' : customers.length}
                    </span>
                  </div>

                  {/* Search and Filters */}
                  <div className="flex items-center gap-4">
                    <div className="flex flex-wrap gap-2">
                      {(['All', 'Active', 'Inactive'] as const).map((status) => (
                        <button
                          key={status}
                          onClick={() => setStatusFilter(status)}
                          className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 ${
                            status === statusFilter
                              ? 'bg-indigo-600 text-white shadow-lg'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {status === 'All' ? common('all') :
                           status === 'Active' ? t('status.active') :
                           status === 'Inactive' ? t('status.inactive') : status} {status !== 'All' && `(${
                            status === 'Active' ? activeCustomers :
                            status === 'Inactive' ? inactiveCustomers : 0
                          })`}
                        </button>
                      ))}
                    </div>

                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder={t('searchCustomers')}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-64 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('customer')}</th>
                      <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('statusLabel')}</th>
                      <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('phone')}</th>
                      <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('orders')}</th>
                      <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('totalSpent')}</th>
                      <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('lastOrderDate')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      // Show loading rows
                      Array.from({ length: 5 }).map((_, index) => (
                        <CustomerRow key={`loading-${index}`} customer={{} as Customer} loading={true} index={index} />
                      ))
                    ) : customers.length > 0 ? (
                      customers.map((customer, index) => (
                        <CustomerRow
                          key={customer.id}
                          customer={customer}
                          loading={false}
                          index={index}
                          onClick={handleCustomerClick}
                          onToggleVip={handleToggleVip}
                        />
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="py-12 px-6 text-center text-gray-500">
                          <div className="flex flex-col items-center">
                            <Users className="w-16 h-16 text-gray-300 mb-4" />
                            <p className="text-lg font-medium mb-2">{t('noCustomersFound')}</p>
                            <p className="text-sm text-gray-400">
                              {searchTerm ? t('tryAdjustingSearch') : t('addFirstCustomer')}
                            </p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* Customer Details Modal */}
      <CustomerDetailsModal
        customer={selectedCustomer}
        isOpen={showCustomerModal}
        onClose={() => {
          setShowCustomerModal(false);
          setSelectedCustomer(null);
        }}
        onSave={handleCustomerSave}
        onDelete={handleCustomerDelete}
      />

      {/* Add Customer Modal */}
      <AddCustomerModal
        isOpen={showAddCustomerModal}
        onClose={() => setShowAddCustomerModal(false)}
        onSave={handleAddCustomer}
      />

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </PageTransition>
  );
};

export default CustomersPage;