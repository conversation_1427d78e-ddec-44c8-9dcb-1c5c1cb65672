import type { NextApiRequest, NextApiResponse } from 'next';
import { createSimpleShopifyClient } from './lib/shopify';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const client = createSimpleShopifyClient();
    
    // Test 1: Get ALL orders (any status)
    console.log('=== Testing ALL orders ===');
    const allOrdersResponse = await client.get('orders', {
      limit: '10',
      status: 'any'
    });
    
    console.log('All orders count:', allOrdersResponse.orders?.length || 0);
    console.log('All orders:', allOrdersResponse.orders?.map((order: { id: number; name: string; financial_status: string; fulfillment_status: string; created_at: string; total_price: string }) => ({
      id: order.id,
      name: order.name,
      financial_status: order.financial_status,
      fulfillment_status: order.fulfillment_status,
      created_at: order.created_at,
      total_price: order.total_price
    })));

    // Test 2: Get OPEN orders
    console.log('=== Testing OPEN orders ===');
    const openOrdersResponse = await client.get('orders', {
      limit: '10',
      status: 'open'
    });
    
    console.log('Open orders count:', openOrdersResponse.orders?.length || 0);
    console.log('Open orders:', openOrdersResponse.orders?.map((order: { id: number; name: string; financial_status: string; fulfillment_status: string }) => ({
      id: order.id,
      name: order.name,
      financial_status: order.financial_status,
      fulfillment_status: order.fulfillment_status
    })));

    // Test 3: Get orders with specific financial status
    console.log('=== Testing PAID orders ===');
    const paidOrdersResponse = await client.get('orders', {
      limit: '10',
      financial_status: 'paid'
    });
    
    console.log('Paid orders count:', paidOrdersResponse.orders?.length || 0);

    res.status(200).json({
      success: true,
      message: 'Order tests completed - check console for details',
      summary: {
        all_orders_count: allOrdersResponse.orders?.length || 0,
        open_orders_count: openOrdersResponse.orders?.length || 0,
        paid_orders_count: paidOrdersResponse.orders?.length || 0,
        sample_orders: allOrdersResponse.orders?.slice(0, 3).map((order: { id: number; name: string; financial_status: string; fulfillment_status: string; total_price: string; created_at: string }) => ({
          id: order.id,
          name: order.name,
          financial_status: order.financial_status,
          fulfillment_status: order.fulfillment_status,
          total_price: order.total_price,
          created_at: order.created_at
        })) || []
      }
    });

  } catch (error: unknown) {
    console.error('Test error:', error);
    res.status(500).json({
      error: 'Test failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
