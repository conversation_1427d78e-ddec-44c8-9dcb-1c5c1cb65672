import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Test basic shop info first
    const shopUrl = `https://${process.env.SHOPIFY_STORE_DOMAIN}/admin/api/2024-10/shop.json`;
    
    console.log('Testing Shopify API with:');
    console.log('Store Domain:', process.env.SHOPIFY_STORE_DOMAIN);
    console.log('Access Token:', process.env.SHOPIFY_ACCESS_TOKEN?.substring(0, 10) + '...');
    console.log('API URL:', shopUrl);

    const response = await fetch(shopUrl, {
      method: 'GET',
      headers: {
        'X-Shopify-Access-Token': process.env.SHOPIFY_ACCESS_TOKEN!,
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return res.status(response.status).json({
        error: `Shopify API Error (${response.status})`,
        details: errorText,
        url: shopUrl
      });
    }

    const data = await response.json();
    console.log('Success! Shop data:', data);

    res.status(200).json({
      success: true,
      message: 'Shopify API connection successful!',
      shop: data.shop,
      credentials_check: {
        store_domain: process.env.SHOPIFY_STORE_DOMAIN,
        access_token_prefix: process.env.SHOPIFY_ACCESS_TOKEN?.substring(0, 10) + '...',
      }
    });

  } catch (error: unknown) {
    console.error('Test error:', error);
    res.status(500).json({
      error: 'Test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      details: error
    });
  }
}
